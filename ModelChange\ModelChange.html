<!DOCTYPE html>
<html lang="zh">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>切机管理 - 设备综合管理</title>
    <link rel="icon" href="../pic/icon/weblogo1.png">
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="ModelChange.css">
    <script src="../js/tabs.js" defer></script>
    <script src="ModelChange.js" defer></script>
    <script src="jig_functions.js" defer></script>
    <script src="model_change_history.js" defer></script>
    <script>
        // 根据URL参数显示对应的tab
        document.addEventListener('DOMContentLoaded', function () {
            const urlParams = new URLSearchParams(window.location.search);
            const tabParam = urlParams.get('tab');

            if (tabParam) {
                // 根据参数显示对应的tab
                switch (tabParam) {
                    case 'jig':
                        switchTab('jig');
                        break;
                    case 'mc':
                        switchTab('mc');
                        break;
                        case 'compare':
                            switchTab('compare');
                            break;
                    default:
                        switchTab('jig');
                }
            }
        });
    </script>
</head>

<body style="margin: 0; padding: 10px;">
    <div class="main-content" style="margin-left: 0; margin-top: 0; padding: 0;">
        <!-- <div class="tabs" style="display: none;"> -->
        <div class="tabs">
            <div class="tabs-left">
                <button class="tab-button active" onclick="switchTab('jig')">金型JIG</button>
                <button class="tab-button" onclick="switchTab('mc')">切机履历</button>
                <button class="tab-button" onclick="switchTab('compare')">型号对比</button>
            </div>
        </div>

        <!-- 切机信息选项卡 -->
        <div id="jigTab" class="tab-content active">
            <div style="display: flex; gap: 10px;">
                <!-- 左侧金型列表 -->
                <div id="Left" style="width: 50%;">
                    <h4 style="margin: 0; padding-left: 5px;">金型信息</h4>
                    <!-- 搜索工具栏 -->
                    <div class="search-toolbar">
                        <label for="cutSearch">CUT</label>
                        <input type="text" id="cutSearch" class="ant-input" placeholder="请输入CUT型号"
                            style="width: 160px;">
                        <div class="button-group">
                            <button id="searchBtn" class="ant-btn ant-btn-primary">查询</button>
                            <button id="resetBtn" class="ant-btn ant-btn-default">重置</button>
                        </div>
                    </div>

                    <div class="CUTlist" style="width: 100%; height: 700px; overflow: auto;">
                        <table class="data-table">
                            <thead>
                            </thead>
                            <tbody>
                            </tbody>
                        </table>
                    </div>
                </div>
                <!-- 右侧JIG列表 -->
                <div id="Right" style="width: 50%;">
                    <h4 style="margin: 0; padding-left: 5px;">JIG信息</h4>
                    <!-- 搜索工具栏 -->
                    <div class="search-toolbar">
                        <label for="lineSearch">LINE</label>
                    <input type="text" id="lineSearch" class="ant-input" style="width: 60px;">
                        <label for="modelSearch" style="padding-left: 10px;">MODEL</label>
                    <input type="text" id="modelSearch" class="ant-input" style="width: 120px;">
                        <label for="jigSearch" style="padding-left: 10px;">JIG</label>
                    <input type="text" id="jigSearch" class="ant-input" style="width: 60px;">
                        <div class="button-group">
                            <button id="editBtn" class="ant-btn ant-btn-primary">编辑</button>
                            <button id="jigSearchBtn" class="ant-btn ant-btn-primary">查询</button>
                            <button id="jigResetBtn" class="ant-btn ant-btn-default">重置</button>
                        </div>
                    </div>

                    <!-- JIG数据表格 -->
                    <div class="line-model-jig-section" style="margin-top: 16px;">
                    <div class="line-model-jig-table-container" style="width: 100%; max-height: 700px; overflow: auto;   background-color: #fff;">
                            <table class="data-table line-model-jig-table">
                                <thead>
                                    <tr>
                                        <th style="width: 80px;">LINE</th>
                                        <th style="width: 120px;">MODEL</th>
                                        <th style="width: 100px;">JIG</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <!-- 默认提示信息 -->
                                    <tr class="default-message">
                                    <td colspan="3" style="text-align: center; padding: 20px; color: #999; font-style: italic;">
                                        请点击查询按钮获取JIG数据
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 切机履历选项卡 -->
        <div id="mcTab" class="tab-content">
            <div>
                <h4 style="margin: 0; padding-left: 5px;">切机履历</h4>
                <!-- 搜索工具栏 -->
                <div class="search-toolbar">
                    <label for="siteSelect">SITE</label>
                    <select id="siteSelect" class="ant-input" style="width: 120px;">
                        <option value="">请选择SITE</option>
                    </select>

                    <label for="eqpSelect" style="padding-left: 10px;">EQP</label>
                    <select id="eqpSelect" class="ant-input" style="width: 120px;">
                        <option value="">请选择EQP</option>
                    </select>

                    <div class="button-group">
                        <button id="inspectionLoginBtn" class="ant-btn ant-btn-primary">点检登录</button>
                        <button id="mcQueryBtn" class="ant-btn ant-btn-primary">查询</button>
                        <button id="mcResetBtn" class="ant-btn ant-btn-default">重置</button>
                    </div>
                </div>

                <!-- 数据表格 -->
                <div class="line-model-jig-section" style="margin-top: 16px;">
                    <div class="line-model-jig-table-container"
                        style="width: 100%; overflow: auto; background-color: #fff;">
                        <table class="mc-history-table" id="mcHistoryTable">
                            <thead>
                                <tr>
                                    <th style="width: 80px;">eqp_id</th>
                                    <th style="width: 80px;">date</th>
                                    <th style="width: 80px;">shift1</th>
                                    <th style="width: 80px;">shift2</th>
                                    <th style="width: 80px;">mc_time</th>
                                    <th style="width: 80px;">mc_times</th>
                                    <th style="width: 80px;">start_time1</th>
                                    <th style="width: 80px;">start_time2</th>
                                    <th style="width: 80px;">s_time</th>
                                    <th style="width: 80px;">mc_type</th>
                                    <th style="width: 80px;">noplan</th>
                                    <th style="width: 80px;">old_model</th>
                                    <th style="width: 80px;">track_product</th>
                                    <th style="width: 80px;">abaflag</th>
                                    <th style="width: 80px;">prod_type</th>
                                    <th style="width: 100px;">点检表</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- 默认提示信息 -->
                                <tr class="default-message">
                                    <td colspan="16" style="text-align: center; padding: 20px; color: #999; font-style: italic;">
                                        请点击查询按钮获取切机履历
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <!-- 分页控件 -->
                    <div id="mcPagination" class="pagination-container" style="margin-top: 16px; text-align: center; display: none;">
                        <button id="mcFirstBtn" class="ant-btn ant-btn-default">首页</button>
                        <button id="mcPrevBtn" class="ant-btn ant-btn-default">上一页</button>
                        <span id="mcPageInfo" style="margin: 0 16px;">第 1 页，共 1 页</span>
                        <button id="mcNextBtn" class="ant-btn ant-btn-default">下一页</button>
                        <button id="mcLastBtn" class="ant-btn ant-btn-default">尾页</button>
                        <div style="margin: 10px 0; display: inline-block; vertical-align: middle;">
                            <label for="mcPageSizeSelect">每页显示:</label>
                            <select id="mcPageSizeSelect" class="ant-input" style="width: 80px; margin: 0 10px;">
                                <option value="10">10</option>
                                <option value="20">20</option>
                                <option value="50">50</option>
                                <option value="100">100</option>
                            </select>
                        </div>
                        <div style="margin: 10px 0; display: inline-block; vertical-align: middle;">
                            <label for="mcPageJumpInput">跳转到:</label>
                            <input type="number" id="mcPageJumpInput" class="ant-input" style="width: 60px; margin: 0 5px;" min="1">
                            <button id="mcPageJumpBtn" class="ant-btn ant-btn-default">GO</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 型号对比选项卡 -->
         <div id="compareTab" class="tab-content">
            <h4 style="margin: 0; padding-left: 5px;">型号对比</h4>
            <iframe src="http://109.120.57.234:7070/mc_plan/main.html" style="width: 100%; height:100vh;border: none; display: block;"></iframe>
        </div>
    </div>

    <!-- 点检登录模态框 -->
    <div id="inspectionModal" class="modal" style="display: none;">
        <div class="modal-content" style="width: 90%; max-width: 1600px;">
            <div class="modal-header">
                <h3>点检登录</h3>
                <span class="close" onclick="closeInspectionModal()">&times;</span>
            </div>
            <div class="modal-body">
                <!-- 点检主表录入 -->
                <h4>点检主表信息</h4>
                <div class="inspection-master-form">
                    <table class="form-table">
                        <thead>
                            <tr>
                                <th class="required">SITEID</th>
                                <th class="required">PROJECT</th>
                                <th class="required">EQPID</th>
                                <th class="required">MC_DATE</th>
                                <th class="required">OLD_PRODUCT</th>
                                <th class="required">TRACK_PRODUCT</th>
                                <th class="required">MC_OPERATOR</th>
                                <th class="required">INSPECTOR</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><input type="text" id="masterSiteId" class="ant-input" required></td>
                                <td>
                                    <select id="masterProject" class="ant-input" required>
                                        <option value="">请选择</option>
                                        <option value="CP">CP</option>
                                        <option value="OLB">OLB</option>
                                    </select>
                                </td>
                                <td><input type="text" id="masterEqpId" class="ant-input" required></td>
                                <td><input type="date" id="masterMcDate" class="ant-input" required></td>
                                <td><input type="text" id="masterOldProduct" class="ant-input" required></td>
                                <td><input type="text" id="masterTrackProduct" class="ant-input" required></td>
                                <td><input type="text" id="masterMcOperator" class="ant-input" required></td>
                                <td><input type="text" id="masterInspector" class="ant-input" required></td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- 点检明细表录入 -->
                <h4 style="margin-top: 20px;">点检明细信息</h4>
                <div class="inspection-detail-form">
                    <table class="form-table" id="detailTable">
                        <thead>
                            <tr>
                                <th>ITEM_NO</th>
                                <th>CHECK_CONTENT</th>
                                <th>METHOD</th>
                                <th>SPEC_LSL</th>
                                <th>SPEC_TAR</th>
                                <th>SPEC_USL</th>
                                <th>PIC_OK</th>
                                <th>PIC_NG</th>
                                <th>MEASURE</th>
                                <th>JUDGEMENT</th>
                            </tr>
                        </thead>
                        <tbody id="detailTableBody">
                            <!-- 动态生成的行 -->
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="modal-footer">
                <button class="ant-btn ant-btn-primary" onclick="submitInspection()">提交</button>
                <button class="ant-btn ant-btn-default" onclick="closeInspectionModal()">取消</button>
            </div>
        </div>
    </div>

    <!-- 点检详情模态框 -->
    <div id="inspectionDetailModal" class="modal" style="display: none;">
        <div class="modal-content" style="width: 90%; max-width: 1600px;">
            <div class="modal-header">
                <h3>点检详情</h3>
                <span class="close" onclick="closeInspectionDetailModal()">&times;</span>
            </div>
            <div class="modal-body">
                <!-- 点检主表显示 -->
                <h4>点检主表信息</h4>
                <div id="inspectionMasterDisplay"></div>

                <!-- 点检明细表显示 -->
                <h4 style="margin-top: 20px;">点检明细信息</h4>
                <div id="inspectionDetailDisplay"></div>
            </div>
            <div class="modal-footer">
                <button class="ant-btn ant-btn-default" onclick="closeInspectionDetailModal()">关闭</button>
            </div>
        </div>
    </div>
</body>

</html>